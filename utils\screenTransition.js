import { StatusBar, Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Settings keys for AsyncStorage - must match those in SettingsContext.js
const SETTINGS_KEYS = {
  DARK_MODE_ENABLED: 'settings_dark_mode_enabled',
};

/**
 * Utility function to ensure consistent StatusBar appearance during screen transitions
 * Call this in the useEffect hook of each screen component
 *
 * @param {boolean} forceDarkMode - Optional parameter to force dark mode appearance
 */
export const ensureStatusBarAppearance = async (forceDarkMode) => {
  try {
    // Check if we should force dark mode or get the current setting
    let isDarkMode = forceDarkMode;

    if (isDarkMode === undefined) {
      // Get the current theme setting from AsyncStorage
      const darkModeValue = await AsyncStorage.getItem(SETTINGS_KEYS.DARK_MODE_ENABLED);
      isDarkMode = darkModeValue === null ? true : darkModeValue === 'true'; // Default to true if not set
    }

    // Set status bar style based on theme
    StatusBar.setBarStyle(isDarkMode ? 'light-content' : 'dark-content', true);

    // Set background color for Android
    if (Platform.OS === 'android') {
      StatusBar.setBackgroundColor(isDarkMode ? '#121212' : '#f5f5f5', true);
      StatusBar.setTranslucent(false);
    }
  } catch (error) {
    console.error('Error in ensureStatusBarAppearance:', error);
    // Fallback to dark mode on error
    StatusBar.setBarStyle('light-content', true);
    if (Platform.OS === 'android') {
      StatusBar.setBackgroundColor('#121212', true);
      StatusBar.setTranslucent(false);
    }
  }
};

export default {
  ensureStatusBarAppearance,
};
