import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Animated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const CustomTabBar = ({ state, descriptors, navigation }) => {
  // Animation values for each tab
  const [animations] = useState(() => {
    try {
      return state.routes.map(() => new Animated.Value(1));
    } catch (error) {
      console.error('Error creating animations:', error);
      // Return a default array with one animation value as fallback
      return [new Animated.Value(1)];
    }
  });

  // Handle tab press with animation
  const handleTabPress = (route, index) => {
    try {
      // Start press animation
      Animated.sequence([
        // Scale down
        Animated.timing(animations[index], {
          toValue: 0.9,
          duration: 100,
          useNativeDriver: true,
        }),
        // Scale back up
        Animated.timing(animations[index], {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start();

      // Navigate to the route
      const event = navigation.emit({
        type: 'tabPress',
        target: route.key,
        canPreventDefault: true,
      });

      if (state.index !== index && !event.defaultPrevented) {
        navigation.navigate(route.name);
      }
    } catch (error) {
      console.error('Tab press error:', error);
      // Fallback navigation without animation
      navigation.navigate(route.name);
    }
  };

  // Reset animations when component unmounts and handle potential errors
  useEffect(() => {
    // Create a cleanup function
    const cleanup = () => {
      if (!animations || !Array.isArray(animations)) {
        console.error('Invalid animations array in cleanup');
        return;
      }

      animations.forEach(anim => {
        try {
          if (anim && typeof anim.setValue === 'function') {
            anim.setValue(1);
          }
        } catch (error) {
          console.error('Error resetting animation:', error);
        }
      });
    };

    // Return the cleanup function
    return cleanup;
  }, [animations]);

  // Safety check for state and routes
  if (!state || !state.routes || !Array.isArray(state.routes) || state.routes.length === 0) {
    console.error('Invalid state or routes in CustomTabBar');
    return <View style={styles.tabBarContainer} />;
  }

  return (
    <View style={styles.tabBarContainer}>
      {state.routes.map((route, index) => {
        // Safety check for route
        if (!route || !route.name) {
          console.error('Invalid route in CustomTabBar');
          return null;
        }
        // Safety check for descriptors
        if (!descriptors || !descriptors[route.key]) {
          console.error(`Descriptor not found for route key: ${route.key}`);
          return null;
        }

        const { options } = descriptors[route.key];
        const label = (options && (options.tabBarLabel || options.title)) || route.name;
        const isFocused = state.index === index;

        // Determine icon based on route and focus state
        let iconName;
        if (route.name === 'HomeTab') {
          iconName = isFocused ? 'home' : 'home-outline';
        } else if (route.name === 'ConnectTab') {
          iconName = isFocused ? 'add-circle' : 'add-circle-outline';
        } else if (route.name === 'SettingsTab') {
          iconName = isFocused ? 'settings' : 'settings-outline';
        }

        // Special styling for the Connect tab
        if (route.name === 'ConnectTab') {
          return (
            <Animated.View
              key={index}
              style={[
                styles.tabItem,
                { transform: [{ scale: animations[index] }] }
              ]}
            >
              <TouchableOpacity
                onPress={() => handleTabPress(route, index)}
                style={styles.connectTabContainer}
              >
                <View style={[styles.connectTab, styles.fallbackConnectTab]}>
                  <Ionicons
                    name={iconName}
                    size={24}
                    color="#FFFFFF"
                  />
                  <Text style={styles.connectTabLabel}>{label}</Text>
                </View>
              </TouchableOpacity>
            </Animated.View>
          );
        }

        // Regular tabs
        return (
          <Animated.View
            key={index}
            style={[
              styles.tabItem,
              { transform: [{ scale: animations[index] }] }
            ]}
          >
            <TouchableOpacity
              onPress={() => handleTabPress(route, index)}
              style={styles.tab}
            >
              <Ionicons
                name={iconName}
                size={24}
                color={isFocused ? '#FFFFFF' : '#9e9e9e'}
              />
              <Text style={[
                styles.tabLabel,
                { color: isFocused ? '#FFFFFF' : '#9e9e9e' }
              ]}>
                {label}
              </Text>
            </TouchableOpacity>
          </Animated.View>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  tabBarContainer: {
    flexDirection: 'row',
    backgroundColor: '#1e1e1e',
    height: 60,
    borderTopColor: '#333333',
    borderTopWidth: 1,
  },
  tabItem: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tab: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 5,
    width: '100%',
  },
  tabLabel: {
    fontSize: 12,
    marginTop: 2,
  },
  connectTabContainer: {
    width: '90%',
    borderRadius: 20,
    overflow: 'hidden',
    marginTop: -15, // Raise the button slightly above the tab bar
    elevation: 5,   // Add shadow for Android
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
  },
  connectTab: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: '#1e1e1e', // Match tab bar color for a nice outline effect
  },
  fallbackConnectTab: {
    backgroundColor: '#d442f5', // Fallback to solid purple if gradient fails
  },
  connectTabLabel: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 5,
  },
});

export default CustomTabBar;
