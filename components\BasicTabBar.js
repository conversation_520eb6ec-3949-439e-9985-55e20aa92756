import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

/**
 * A very basic tab bar component with a special connect button
 */
const BasicTabBar = ({ state, descriptors, navigation }) => {
  if (!state || !state.routes || !Array.isArray(state.routes)) {
    return null;
  }

  return (
    <View style={styles.container}>
      {state.routes.map((route, index) => {
        if (!route || !route.key || !descriptors || !descriptors[route.key]) {
          return null;
        }

        const { options } = descriptors[route.key];
        const label = options?.title || route.name || '';
        const isFocused = state.index === index;

        // Determine icon based on route
        let iconName;
        if (route.name === 'HomeTab') {
          iconName = isFocused ? 'home' : 'home-outline';
        } else if (route.name === 'ConnectTab') {
          iconName = isFocused ? 'add-circle' : 'add-circle-outline';
        } else if (route.name === 'SettingsTab') {
          iconName = isFocused ? 'settings' : 'settings-outline';
        } else {
          iconName = 'ellipsis-horizontal';
        }

        // Handle tab press
        const onPress = () => {
          try {
            const event = navigation.emit({
              type: 'tabPress',
              target: route.key,
              canPreventDefault: true,
            });

            if (!isFocused && !event.defaultPrevented) {
              navigation.navigate(route.name);
            }
          } catch (error) {
            console.error('Tab navigation error:', error);
          }
        };

        // Special styling for Connect tab
        if (route.name === 'ConnectTab') {
          return (
            <View key={route.key} style={styles.tabItem}>
              <View style={styles.connectButtonContainer}>
                <TouchableOpacity
                  onPress={onPress}
                  style={styles.connectButton}
                  activeOpacity={0.6} // This creates a darker effect when pressed
                >
                  <Ionicons name={iconName} size={28} color="#FFFFFF" />
                </TouchableOpacity>
                <Text style={styles.connectLabel}>{label}</Text>
              </View>
            </View>
          );
        }

        // Regular tab
        return (
          <View key={route.key} style={styles.tabItem}>
            <TouchableOpacity
              onPress={onPress}
              activeOpacity={0.7}
              style={styles.tab}
            >
              <Ionicons
                name={iconName}
                size={24}
                color={isFocused ? '#FFFFFF' : '#9e9e9e'}
              />
              <Text style={[
                styles.label,
                { color: isFocused ? '#FFFFFF' : '#9e9e9e' }
              ]}>
                {label}
              </Text>
            </TouchableOpacity>
          </View>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: '#1e1e1e',
    height: 70,
    borderTopColor: '#333333',
    borderTopWidth: 1,
    paddingBottom: 5,
  },
  tabItem: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tab: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 5,
    width: '100%',
  },
  label: {
    fontSize: 12,
    marginTop: 2,
  },
  connectButtonContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: -25,
  },
  connectButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#d442f5',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#1e1e1e',
    // This will darken when pressed due to activeOpacity
  },
  connectLabel: {
    color: '#FFFFFF',
    fontSize: 11,
    fontWeight: 'bold',
    marginTop: 4,
  },
});

export default BasicTabBar;
