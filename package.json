{"name": "synaptix", "license": "0BSD", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "expo": {}, "dependencies": {"@expo/cli": "^0.24.13", "@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-picker/picker": "^2.11.0", "@react-native/codegen": "^0.79.2", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "base-64": "^1.0.0", "buffer": "^6.0.3", "crypto-browserify": "^3.12.1", "events": "^3.3.0", "expo": "^53.0.9", "expo-blur": "^14.1.4", "expo-dev-client": "~5.1.8", "expo-device": "^7.1.4", "expo-file-system": "~18.1.9", "expo-image-manipulator": "~13.1.6", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "^14.1.4", "expo-notifications": "^0.31.1", "expo-secure-store": "^14.2.3", "expo-status-bar": "~2.2.3", "process": "^0.11.10", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-get-random-values": "^1.11.0", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.10.0", "react-native-svg": "^15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "stream-browserify": "^3.0.0", "util": "^0.12.5", "whatwg-url": "^14.2.0"}, "devDependencies": {"@babel/core": "^7.20.0", "babel-plugin-module-resolver": "^5.0.2"}, "private": true}