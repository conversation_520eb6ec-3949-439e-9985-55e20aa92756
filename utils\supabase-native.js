// Mock Supabase client for native platforms
// Authentication is now handled locally in AuthContext

const mockSupabase = {
  auth: {
    session: async () => ({ data: null }),
    signIn: async () => ({ data: null, error: { message: 'Supabase disabled' } }),
    signUp: async () => ({ data: null, error: { message: 'Supabase disabled' } }),
    signOut: async () => ({ error: null }),
  },
  from: () => ({
    select: () => ({
      eq: () => ({
        single: async () => ({ data: null, error: null }),
      }),
    }),
    upsert: async () => ({ error: null }),
  }),
};

export default mockSupabase;
