import React from 'react';
import { StatusBar, View, StyleSheet } from 'react-native';
import { useSettings } from '../contexts/SettingsContext';

/**
 * A global status bar component that ensures consistent status bar appearance
 * across all screens and during navigation transitions
 */
const GlobalStatusBar = () => {
  const { darkModeEnabled } = useSettings();

  return (
    <View style={[
      styles.statusBarContainer,
      { backgroundColor: darkModeEnabled ? '#121212' : '#f5f5f5' }
    ]}>
      <StatusBar
        barStyle={darkModeEnabled ? "light-content" : "dark-content"}
        backgroundColor={darkModeEnabled ? "#121212" : "#f5f5f5"}
        translucent={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  statusBarContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 999, // Ensure it stays on top of other components
  },
});

export default GlobalStatusBar;
