import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSettings } from '../contexts/SettingsContext';

/**
 * A minimal tab bar component with no complex logic or animations
 * Designed to avoid call stack and render errors
 */
const MinimalTabBar = ({ state, descriptors, navigation }) => {
  // Get theme settings
  const { darkModeEnabled } = useSettings();

  // Basic safety check
  if (!state || !navigation) {
    return <View style={[
      styles.container,
      { backgroundColor: darkModeEnabled ? '#1e1e1e' : '#ffffff' }
    ]} />;
  }

  // Get the routes safely
  const routes = Array.isArray(state.routes) ? state.routes : [];

  return (
    <View style={[
      styles.container,
      {
        backgroundColor: darkModeEnabled ? '#1e1e1e' : '#ffffff',
        borderTopColor: darkModeEnabled ? '#333333' : '#e0e0e0'
      }
    ]}>
      {routes.map((route, index) => {
        // Skip invalid routes
        if (!route || typeof route !== 'object') return null;

        // Get the key safely
        const key = route.key || `tab-${index}`;

        // Get the name safely
        const name = route.name || '';

        // Get the descriptor safely
        const descriptor = (descriptors && descriptors[key]) || {};

        // Get the options safely
        const options = descriptor.options || {};

        // Get the label safely
        const label = options.title || name;

        // Check if this tab is focused
        const isFocused = state.index === index;

        // Determine icon based on route name
        let iconName = 'help-circle-outline';
        if (name === 'HomeTab') {
          iconName = isFocused ? 'home' : 'home-outline';
        } else if (name === 'ConnectTab') {
          // Always use the same icon for connect tab regardless of focus state
          iconName = 'add-circle-outline';
        } else if (name === 'SettingsTab') {
          iconName = isFocused ? 'settings' : 'settings-outline';
        }

        // Safe navigation function
        const onPress = () => {
          try {
            // Only navigate if not already on this tab
            if (!isFocused && navigation && typeof navigation.navigate === 'function') {
              navigation.navigate(name);
            }
          } catch (error) {
            // Silently handle errors to prevent crashes
            console.error('Navigation error:', error);
          }
        };

        // Special styling for Connect tab
        if (name === 'ConnectTab') {
          return (
            <View key={key} style={styles.tabItem}>
              <View style={styles.connectButtonContainer}>
                <TouchableOpacity
                  onPress={onPress}
                  style={[
                    styles.connectButton,
                    { borderColor: darkModeEnabled ? '#1e1e1e' : '#ffffff' }
                  ]}
                  activeOpacity={0.5} // Lower value for more visible effect when pressed
                >
                  <Ionicons
                    name={iconName}
                    size={28}
                    color="#FFFFFF" // Always white regardless of focus state
                  />
                </TouchableOpacity>
                <Text style={[
                  styles.connectLabel,
                  { color: darkModeEnabled ? '#FFFFFF' : '#d442f5' } // Keep Connect label color unchanged
                ]}>{label}</Text>
              </View>
            </View>
          );
        }

        // Regular tab
        return (
          <View key={key} style={styles.tabItem}>
            <TouchableOpacity
              onPress={onPress}
              style={styles.tab}
              activeOpacity={0.7}
            >
              <Ionicons
                name={iconName}
                size={24}
                color={isFocused
                  ? (darkModeEnabled ? '#cccccc' : '#666666') // Light white in dark mode, dark grey in light mode when active
                  : (darkModeEnabled ? '#9e9e9e' : '#757575')
                }
              />
              <Text style={[
                styles.label,
                {
                  color: isFocused
                    ? (darkModeEnabled ? '#cccccc' : '#666666') // Light white in dark mode, dark grey in light mode when active
                    : (darkModeEnabled ? '#9e9e9e' : '#757575')
                }
              ]}>
                {label}
              </Text>
            </TouchableOpacity>
          </View>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    height: 70,
    borderTopWidth: 1,
    paddingBottom: 5,
    // backgroundColor and borderTopColor are set dynamically
  },
  tabItem: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tab: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 5,
    width: '100%',
  },
  label: {
    fontSize: 12,
    marginTop: 2,
  },
  connectButtonContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: -25,
  },
  connectButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#d442f5', // Always keep this color regardless of screen
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#1e1e1e',
  },
  connectLabel: {
    fontSize: 11,
    fontWeight: 'bold',
    marginTop: 4,
    // color is set dynamically
  },
});

export default MinimalTabBar;
