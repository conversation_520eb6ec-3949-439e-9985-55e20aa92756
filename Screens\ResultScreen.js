import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ActivityIndicator, ScrollView, StatusBar } from 'react-native';
import GradientButton from '../components/GradientButton';
import { useSettings } from '../contexts/SettingsContext';
import apiService from '../utils/apiService';

export default function ResultScreen({ navigation }) {
  const [loading, setLoading] = useState(true);
  const [results, setResults] = useState(null);
  const { darkModeEnabled } = useSettings();

  useEffect(() => {
    // Function to get prediction results
    const getPredictionResults = async () => {
      try {
        // Simulate loading data
        setTimeout(async () => {
          try {
            // Generate mock data
            const mockData = {
              timestamp: Date.now(),
              channels: [180, 200, 160, 190, 210, 175, 195, 185] // Mock channel values
            };

            // Get prediction results
            const mockResults = await apiService.getPrediction(mockData);
            setResults(mockResults);
          } catch (error) {
            console.error('Error getting mock prediction:', error);
            // Fallback to mock data
            setResults(apiService.getMockPrediction());
          }

          setLoading(false);
        }, 1500);
      } catch (error) {
        console.error('Unexpected error in prediction flow:', error);
        setResults(apiService.getMockPrediction());
        setLoading(false);
      }
    };

    // Start the prediction process
    getPredictionResults();

    // Cleanup function
    return () => {
      // Any cleanup code here
    };
  }, []);

  const renderScoreBar = (score, color) => {
    return (
      <View style={styles.scoreBarContainer}>
        <View style={[styles.scoreBar, { width: `${score}%`, backgroundColor: color }]} />
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <StatusBar barStyle="light-content" />
        <ActivityIndicator size="large" color="#d442f5" />
        <Text style={styles.loadingText}>Analyzing brain patterns...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <StatusBar barStyle="light-content" />
      <View style={styles.header}>
        <Text style={styles.title}>Scan Results</Text>
      </View>

      <View style={styles.scoreContainer}>
        <View style={styles.overallScoreCircle}>
          <Text style={styles.overallScoreText}>{results.overallScore}</Text>
        </View>
        <Text style={styles.overallScoreLabel}>Overall Brain Performance</Text>
      </View>

      <View style={styles.categoriesContainer}>
        <Text style={styles.sectionTitle}>Performance by Category</Text>

        {results.categories.map((category, index) => (
          <View key={index} style={styles.categoryItem}>
            <View style={styles.categoryHeader}>
              <Text style={styles.categoryName}>{category.name}</Text>
              <Text style={styles.categoryScore}>{category.score}%</Text>
            </View>
            {renderScoreBar(category.score, category.color)}
          </View>
        ))}
      </View>

      <View style={styles.recommendationsContainer}>
        <Text style={styles.sectionTitle}>Recommendations</Text>

        {results.recommendations.map((recommendation, index) => (
          <View key={index} style={styles.recommendationItem}>
            <Text style={styles.recommendationBullet}>•</Text>
            <Text style={styles.recommendationText}>{recommendation}</Text>
          </View>
        ))}
      </View>

      <View style={styles.buttonContainer}>
        <GradientButton
          text="BACK TO CONNECT"
          onPress={() => {
            navigation.navigate('ConnectTab');
          }}
          style={styles.backButton}
        />
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#121212',
  },
  loadingText: {
    color: '#ffffff',
    marginTop: 20,
    fontSize: 18,
  },
  header: {
    padding: 20,
    paddingTop: 40,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  scoreContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  overallScoreCircle: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#1e1e1e',
    borderWidth: 4,
    borderColor: '#ffffff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  overallScoreText: {
    fontSize: 40,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  overallScoreLabel: {
    fontSize: 16,
    color: '#ffffff',
    textAlign: 'center',
  },
  categoriesContainer: {
    padding: 20,
    backgroundColor: '#1e1e1e',
    marginHorizontal: 20,
    borderRadius: 10,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 15,
  },
  categoryItem: {
    marginBottom: 15,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
  },
  categoryName: {
    fontSize: 16,
    color: '#e0e0e0',
  },
  categoryScore: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  scoreBarContainer: {
    height: 10,
    backgroundColor: '#333333',
    borderRadius: 5,
    overflow: 'hidden',
  },
  scoreBar: {
    height: '100%',
  },
  recommendationsContainer: {
    padding: 20,
    backgroundColor: '#1e1e1e',
    marginHorizontal: 20,
    borderRadius: 10,
    marginBottom: 20,
  },
  recommendationItem: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  recommendationBullet: {
    color: '#ffffff',
    fontSize: 18,
    marginRight: 10,
  },
  recommendationText: {
    color: '#e0e0e0',
    fontSize: 16,
    flex: 1,
    lineHeight: 22,
  },
  buttonContainer: {
    marginHorizontal: 20,
    marginBottom: 30,
  },
  backButton: {
    marginBottom: 10,
  },

});
