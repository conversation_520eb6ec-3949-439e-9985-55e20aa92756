import React, { createContext, useState, useEffect, useContext } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';

// Create the authentication context
const AuthContext = createContext();

// Custom hook to use the auth context
export const useAuth = () => useContext(AuthContext);

// Helper function to get saved accounts
const getSavedAccounts = async () => {
  try {
    const savedAccountsJson = await AsyncStorage.getItem('savedAccounts');
    return savedAccountsJson ? JSON.parse(savedAccountsJson) : [];
  } catch (error) {
    console.error('Error getting saved accounts:', error);
    return [];
  }
};

// Helper function to get all registered users
const getRegisteredUsers = async () => {
  try {
    const usersJson = await AsyncStorage.getItem('registeredUsers');
    return usersJson ? JSON.parse(usersJson) : [];
  } catch (error) {
    console.error('Error getting registered users:', error);
    return [];
  }
};

// Helper function to save registered users
const saveRegisteredUsers = async (users) => {
  try {
    await AsyncStorage.setItem('registeredUsers', JSON.stringify(users));
  } catch (error) {
    console.error('Error saving registered users:', error);
  }
};

// Helper function to save user account
const saveUserAccount = async (user) => {
  try {
    // Get existing saved accounts
    const savedAccounts = await getSavedAccounts();

    // Check if this account already exists
    const existingAccountIndex = savedAccounts.findIndex(
      account => account.email.toLowerCase() === user.email.toLowerCase()
    );

    // Create a copy of the user object with the password if available
    const userToSave = {
      ...user,
      lastUsed: new Date().toISOString(),
    };

    if (existingAccountIndex >= 0) {
      // Update existing account
      savedAccounts[existingAccountIndex] = {
        ...savedAccounts[existingAccountIndex],
        ...userToSave,
      };
    } else {
      // Add new account
      savedAccounts.push(userToSave);
    }

    // Save the updated accounts list
    await AsyncStorage.setItem('savedAccounts', JSON.stringify(savedAccounts));
    return true;
  } catch (error) {
    console.error('Error saving user account:', error);
    return false;
  }
};

// Helper function to update the last used timestamp for an account
const updateLastUsed = async (email) => {
  try {
    const savedAccounts = await getSavedAccounts();
    const accountIndex = savedAccounts.findIndex(
      account => account.email.toLowerCase() === email.toLowerCase()
    );

    if (accountIndex >= 0) {
      savedAccounts[accountIndex].lastUsed = new Date().toISOString();
      await AsyncStorage.setItem('savedAccounts', JSON.stringify(savedAccounts));
    }
    return true;
  } catch (error) {
    console.error('Error updating last used timestamp:', error);
    return false;
  }
};

// Provider component that wraps the app and makes auth object available to any child component that calls useAuth()
export function AuthProvider({ children }) {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [savedAccounts, setSavedAccounts] = useState([]);
  const [showAccountSwitcher, setShowAccountSwitcher] = useState(false);
  const [showDataPermissionPrompt, setShowDataPermissionPrompt] = useState(false);

  // Load user data on app start
  useEffect(() => {
    const loadUserData = async () => {
      try {
        // Load saved accounts
        const accounts = await getSavedAccounts();
        setSavedAccounts(accounts);

        // Check for an existing Supabase session (using v1.x API)
        const sessionResponse = await supabase.auth.session();
        const session = sessionResponse?.data || null;

        if (session) {
          // Get the current user (in v1.x, user is available in the session)
          const user = session.user;

          if (user) {
            // Get user profile data from the profiles table
            const { data: profileData, error: profileError } = await supabase
              .from('profiles')
              .select('*')
              .eq('id', user.id)
              .single();

            if (profileError && profileError.code !== 'PGRST116') {
              console.error('Error fetching profile on load:', profileError);
            }

            // Combine auth data with profile data
            const userData = {
              id: user.id,
              email: user.email,
              name: profileData?.name || user.email.split('@')[0],
              dob: profileData?.dob || '',
              gender: profileData?.gender || 'male',
              phone: profileData?.phone || '',
              address: profileData?.address || '',
              profilePicture: profileData?.profile_picture_url || null,
            };

            // Save the current user to AsyncStorage for persistence
            await AsyncStorage.setItem('lastLoggedInUser', JSON.stringify(userData));
            setCurrentUser(userData);

            // Update last used timestamp if this is a saved account
            const savedAccount = accounts.find(
              account => account.email.toLowerCase() === userData.email.toLowerCase()
            );
            if (savedAccount) {
              await updateLastUsed(userData.email);
            }
          }
        } else {
          // Check if we have a previously logged in user in AsyncStorage
          const lastLoggedInUserJson = await AsyncStorage.getItem('lastLoggedInUser');
          if (lastLoggedInUserJson) {
            const lastLoggedInUser = JSON.parse(lastLoggedInUserJson);

            // We have a user in AsyncStorage but no active session
            // This means we need to sign in again, but we can show the user info
            setCurrentUser(null);
          }
        }
      } catch (error) {
        console.error('Error loading user data:', error);
        // Even if there's an error, we should still set the user to null
        // to allow the app to proceed to the login screen
        setCurrentUser(null);
      } finally {
        setLoading(false);
      }
    };

    loadUserData();
  }, []);

  // Login function
  const login = async (email, password) => {
    try {
      // Get registered users from local storage
      const registeredUsers = await getRegisteredUsers();

      // Find user by email
      const user = registeredUsers.find(
        u => u.email.toLowerCase() === email.toLowerCase()
      );

      if (!user) {
        return {
          success: false,
          error: 'Account has not been registered. Please sign up first.'
        };
      }

      // Check password
      if (user.password !== password) {
        return {
          success: false,
          error: 'Incorrect password. Please try again.'
        };
      }

      // Create user data object (excluding password for security)
      const userData = {
        id: user.id,
        email: user.email,
        name: user.name,
        dob: user.dob || '',
        gender: user.gender || 'male',
        phone: user.phone || '',
        address: user.address || '',
        profilePicture: user.profilePicture || null,
      };

      // Save the current user to AsyncStorage for persistence
      await AsyncStorage.setItem('lastLoggedInUser', JSON.stringify(userData));

      setCurrentUser(userData);

      // Find this user in saved accounts
      const accounts = await getSavedAccounts();
      const savedAccount = accounts.find(
        account => account.email.toLowerCase() === email.toLowerCase()
      );

      // Update last used timestamp if this is a saved account
      if (savedAccount) {
        await updateLastUsed(email);
      }

      return { success: true };
    } catch (error) {
      console.error('Login error:', error);
      return {
        success: false,
        error: 'Login failed. Please try again.'
      };
    }
  };

  // Signup function
  const signup = async (userData) => {
    try {
      // Get registered users from local storage
      const registeredUsers = await getRegisteredUsers();

      // Check if user already exists
      const existingUser = registeredUsers.find(
        u => u.email.toLowerCase() === userData.email.toLowerCase()
      );

      if (existingUser) {
        return {
          success: false,
          error: 'Account has already been registered. Please login instead.'
        };
      }

      // Create new user with unique ID
      const newUser = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        email: userData.email,
        password: userData.password, // In a real app, this should be hashed
        name: userData.name,
        dob: userData.dob || '',
        gender: userData.gender || 'male',
        phone: userData.phone || '',
        address: userData.address || '',
        profilePicture: userData.profilePicture || null,
        createdAt: new Date().toISOString(),
      };

      // Add to registered users
      registeredUsers.push(newUser);
      await saveRegisteredUsers(registeredUsers);

      // Create user object for current session (excluding password)
      const userSession = {
        id: newUser.id,
        email: newUser.email,
        name: newUser.name,
        dob: newUser.dob,
        gender: newUser.gender,
        phone: newUser.phone,
        address: newUser.address,
        profilePicture: newUser.profilePicture,
      };

      // Save the current user to AsyncStorage for persistence
      await AsyncStorage.setItem('lastLoggedInUser', JSON.stringify(userSession));

      setCurrentUser(userSession);

      // Show data permission prompt first, then save prompt will be shown after
      setShowDataPermissionPrompt(true);

      return { success: true };
    } catch (error) {
      console.error('Signup error:', error);
      return {
        success: false,
        error: 'Registration failed. Please try again.'
      };
    }
  };

  // Logout function
  const logout = async () => {
    try {
      // Remove the last logged in user
      await AsyncStorage.removeItem('lastLoggedInUser');

      // Clear current user state
      setCurrentUser(null);

      return { success: true };
    } catch (error) {
      console.error('Logout error:', error);
      return {
        success: false,
        error: 'Logout failed. Please try again.'
      };
    }
  };

  // Update user profile
  const updateProfile = async (updatedData) => {
    try {
      if (!currentUser?.id) {
        return {
          success: false,
          error: 'User not authenticated'
        };
      }

      // Update user in local storage
      const registeredUsers = await getRegisteredUsers();
      const userIndex = registeredUsers.findIndex(u => u.id === currentUser.id);

      if (userIndex !== -1) {
        // Update the user data
        registeredUsers[userIndex] = {
          ...registeredUsers[userIndex],
          name: updatedData.name || registeredUsers[userIndex].name,
          dob: updatedData.dob || registeredUsers[userIndex].dob,
          gender: updatedData.gender || registeredUsers[userIndex].gender,
          phone: updatedData.phone || registeredUsers[userIndex].phone,
          address: updatedData.address || registeredUsers[userIndex].address,
          profilePicture: updatedData.profilePicture || registeredUsers[userIndex].profilePicture,
          updatedAt: new Date().toISOString(),
        };

        await saveRegisteredUsers(registeredUsers);
      }

      // Update current user state
      const updatedUser = { ...currentUser, ...updatedData };

      // Save the updated user to AsyncStorage for persistence
      await AsyncStorage.setItem('lastLoggedInUser', JSON.stringify(updatedUser));

      setCurrentUser(updatedUser);

      // Update saved account if it exists
      const accounts = await getSavedAccounts();
      const savedAccount = accounts.find(
        account => account.email.toLowerCase() === currentUser.email.toLowerCase()
      );

      if (savedAccount) {
        await saveUserAccount(updatedUser);
      }

      return { success: true };
    } catch (error) {
      console.error('Update profile error:', error);
      return {
        success: false,
        error: 'Update failed. Please try again.'
      };
    }
  };

  // Save the current user account for persistence
  const saveAccount = async () => {
    try {
      if (!currentUser) {
        return {
          success: false,
          error: 'No user to save'
        };
      }

      const result = await saveUserAccount(currentUser);

      if (result) {
        // Refresh saved accounts list
        const accounts = await getSavedAccounts();
        setSavedAccounts(accounts);

        return { success: true };
      }

      return {
        success: false,
        error: 'Failed to save account'
      };
    } catch (error) {
      console.error('Save account error:', error);
      return {
        success: false,
        error: 'Failed to save account'
      };
    }
  };

  // Switch to a different account
  const switchAccount = async (account) => {
    try {
      // We need the password to authenticate locally
      if (!account.password) {
        // If we don't have the password, we need to redirect to login
        setCurrentUser(null);
        return {
          success: false,
          needsPassword: true,
          email: account.email
        };
      }

      // Get registered users and verify credentials
      const registeredUsers = await getRegisteredUsers();
      const user = registeredUsers.find(
        u => u.email.toLowerCase() === account.email.toLowerCase()
      );

      if (!user) {
        setCurrentUser(null);
        return {
          success: false,
          needsPassword: true,
          email: account.email,
          error: 'Account not found. Please try again.'
        };
      }

      // Check password
      if (user.password !== account.password) {
        setCurrentUser(null);
        return {
          success: false,
          needsPassword: true,
          email: account.email,
          error: 'Incorrect password. Please try again.'
        };
      }

      // Create user data object (excluding password)
      const userData = {
        id: user.id,
        email: user.email,
        name: user.name,
        dob: user.dob || '',
        gender: user.gender || 'male',
        phone: user.phone || '',
        address: user.address || '',
        profilePicture: user.profilePicture || null,
      };

      // Save the current user to AsyncStorage for persistence
      await AsyncStorage.setItem('lastLoggedInUser', JSON.stringify(userData));

      // Set the current user
      setCurrentUser(userData);

      // Update last used timestamp
      await updateLastUsed(account.email);

      // Close account switcher
      setShowAccountSwitcher(false);

      return { success: true };
    } catch (error) {
      console.error('Switch account error:', error);
      return { success: false };
    }
  };

  // Handle data permission acceptance
  const handleDataPermission = async (accepted) => {
    // Close the data permission prompt
    setShowDataPermissionPrompt(false);

    // Store the user's data permission preference
    if (currentUser) {
      // Save the preference to AsyncStorage
      await AsyncStorage.setItem('dataPermissionAccepted', JSON.stringify({
        userId: currentUser.id,
        accepted: accepted,
        timestamp: new Date().toISOString()
      }));

      // If accepted, we save the account information
      // If declined, we don't save any account information
      if (accepted) {
        await saveUserAccount(currentUser);
        // Refresh saved accounts list
        const updatedAccounts = await getSavedAccounts();
        setSavedAccounts(updatedAccounts);
      }

      // Navigate to home after handling data permission
      // This is handled in the SignupScreen component
    }
  };

  // Context value
  const value = {
    currentUser,
    loading,
    savedAccounts,
    showAccountSwitcher,
    setShowAccountSwitcher,
    showDataPermissionPrompt,
    setShowDataPermissionPrompt,
    login,
    signup,
    logout,
    updateProfile,
    saveAccount,
    switchAccount,
    handleDataPermission,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}
