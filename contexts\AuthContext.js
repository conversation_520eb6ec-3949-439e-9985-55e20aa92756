import React, { createContext, useState, useEffect, useContext } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import supabase from '../utils/supabase-platform';

// Create the authentication context
const AuthContext = createContext();

// Custom hook to use the auth context
export const useAuth = () => useContext(AuthContext);

// Helper function to get saved accounts
const getSavedAccounts = async () => {
  try {
    const savedAccountsJson = await AsyncStorage.getItem('savedAccounts');
    return savedAccountsJson ? JSON.parse(savedAccountsJson) : [];
  } catch (error) {
    console.error('Error getting saved accounts:', error);
    return [];
  }
};

// Helper function to save user account
const saveUserAccount = async (user) => {
  try {
    // Get existing saved accounts
    const savedAccounts = await getSavedAccounts();

    // Check if this account already exists
    const existingAccountIndex = savedAccounts.findIndex(
      account => account.email.toLowerCase() === user.email.toLowerCase()
    );

    // Create a copy of the user object with the password if available
    const userToSave = {
      ...user,
      lastUsed: new Date().toISOString(),
    };

    if (existingAccountIndex >= 0) {
      // Update existing account
      savedAccounts[existingAccountIndex] = {
        ...savedAccounts[existingAccountIndex],
        ...userToSave,
      };
    } else {
      // Add new account
      savedAccounts.push(userToSave);
    }

    // Save the updated accounts list
    await AsyncStorage.setItem('savedAccounts', JSON.stringify(savedAccounts));
    return true;
  } catch (error) {
    console.error('Error saving user account:', error);
    return false;
  }
};

// Helper function to update the last used timestamp for an account
const updateLastUsed = async (email) => {
  try {
    const savedAccounts = await getSavedAccounts();
    const accountIndex = savedAccounts.findIndex(
      account => account.email.toLowerCase() === email.toLowerCase()
    );

    if (accountIndex >= 0) {
      savedAccounts[accountIndex].lastUsed = new Date().toISOString();
      await AsyncStorage.setItem('savedAccounts', JSON.stringify(savedAccounts));
    }
    return true;
  } catch (error) {
    console.error('Error updating last used timestamp:', error);
    return false;
  }
};

// Provider component that wraps the app and makes auth object available to any child component that calls useAuth()
export function AuthProvider({ children }) {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [savedAccounts, setSavedAccounts] = useState([]);
  const [showAccountSwitcher, setShowAccountSwitcher] = useState(false);
  const [showDataPermissionPrompt, setShowDataPermissionPrompt] = useState(false);

  // Load user data on app start
  useEffect(() => {
    const loadUserData = async () => {
      try {
        // Load saved accounts
        const accounts = await getSavedAccounts();
        setSavedAccounts(accounts);

        // Check for an existing Supabase session (using v1.x API)
        const sessionResponse = await supabase.auth.session();
        const session = sessionResponse?.data || null;

        if (session) {
          // Get the current user (in v1.x, user is available in the session)
          const user = session.user;

          if (user) {
            // Get user profile data from the profiles table
            const { data: profileData, error: profileError } = await supabase
              .from('profiles')
              .select('*')
              .eq('id', user.id)
              .single();

            if (profileError && profileError.code !== 'PGRST116') {
              console.error('Error fetching profile on load:', profileError);
            }

            // Combine auth data with profile data
            const userData = {
              id: user.id,
              email: user.email,
              name: profileData?.name || user.email.split('@')[0],
              dob: profileData?.dob || '',
              gender: profileData?.gender || 'male',
              phone: profileData?.phone || '',
              address: profileData?.address || '',
              profilePicture: profileData?.profile_picture_url || null,
            };

            // Save the current user to AsyncStorage for persistence
            await AsyncStorage.setItem('lastLoggedInUser', JSON.stringify(userData));
            setCurrentUser(userData);

            // Update last used timestamp if this is a saved account
            const savedAccount = accounts.find(
              account => account.email.toLowerCase() === userData.email.toLowerCase()
            );
            if (savedAccount) {
              await updateLastUsed(userData.email);
            }
          }
        } else {
          // Check if we have a previously logged in user in AsyncStorage
          const lastLoggedInUserJson = await AsyncStorage.getItem('lastLoggedInUser');
          if (lastLoggedInUserJson) {
            const lastLoggedInUser = JSON.parse(lastLoggedInUserJson);

            // We have a user in AsyncStorage but no active session
            // This means we need to sign in again, but we can show the user info
            setCurrentUser(null);
          }
        }
      } catch (error) {
        console.error('Error loading user data:', error);
        // Even if there's an error, we should still set the user to null
        // to allow the app to proceed to the login screen
        setCurrentUser(null);
      } finally {
        setLoading(false);
      }
    };

    loadUserData();
  }, []);

  // Login function
  const login = async (email, password) => {
    try {
      // Sign in with Supabase (using v1.x API)
      const response = await supabase.auth.signIn({
        email,
        password,
      });

      const data = response?.data || null;
      const error = response?.error || null;
      const user = data?.user;

      if (error) {
        console.error('Supabase login error:', error);

        // Provide more specific error messages based on the error code/message
        if (error.message.includes('Invalid login credentials')) {
          // For better user experience, we'll use a more reliable approach

          // When we get "Invalid login credentials" from Supabase, it could mean:
          // 1. The account exists but the password is wrong
          // 2. The account doesn't exist

          // Since we can't reliably distinguish between these cases without admin access,
          // we'll assume the most likely case - that the password is wrong
          return {
            success: false,
            error: 'Incorrect password. Please try again.'
          };
        } else if (error.message.includes('Email not confirmed')) {
          return {
            success: false,
            error: 'Email not verified. Please check your inbox and verify your email.'
          };
        } else {
          // Return the original error message for other types of errors
          return {
            success: false,
            error: error.message || 'Login failed. Please check your credentials and try again.'
          };
        }
      }

      if (!user) {
        return {
          success: false,
          error: 'Account has not been registered. Please sign up first.'
        };
      }

      // Get user profile data from the profiles table
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (profileError && profileError.code !== 'PGRST116') {
        console.error('Error fetching profile:', profileError);
      }

      // Combine auth data with profile data
      const userData = {
        id: user.id,
        email: user.email,
        name: profileData?.name || user.email.split('@')[0],
        dob: profileData?.dob || '',
        gender: profileData?.gender || 'male',
        phone: profileData?.phone || '',
        address: profileData?.address || '',
        profilePicture: profileData?.profile_picture_url || null,
      };

      // Save the current user to AsyncStorage for persistence
      await AsyncStorage.setItem('lastLoggedInUser', JSON.stringify(userData));

      setCurrentUser(userData);

      // Find this user in saved accounts
      const accounts = await getSavedAccounts();
      const savedAccount = accounts.find(
        account => account.email.toLowerCase() === email.toLowerCase()
      );

      // Update last used timestamp if this is a saved account
      if (savedAccount) {
        await updateLastUsed(email);
      }
      // We no longer save accounts automatically without permission

      return { success: true };
    } catch (error) {
      console.error('Login error:', error);
      return {
        success: false,
        error: 'Login failed. Please try again.'
      };
    }
  };

  // Signup function
  const signup = async (userData) => {
    try {
      // Sign up directly with Supabase (using v1.x API)
      // We'll let Supabase handle the check for existing accounts
      // This is more reliable than our previous approach
      const response = await supabase.auth.signUp({
        email: userData.email,
        password: userData.password,
        options: {
          data: {
            name: userData.name,
          },
        }
      });

      const data = response?.data || null;
      const error = response?.error || null;
      const user = data?.user;

      if (error) {
        console.error('Supabase signup error:', error);

        // Provide more specific error messages based on the error
        if (error.message.includes('already registered')) {
          return {
            success: false,
            error: 'Account has already been registered. Please login instead.'
          };
        } else if (error.message.includes('password')) {
          return {
            success: false,
            error: 'Password is too weak. Please use a stronger password.'
          };
        } else if (error.message.includes('email')) {
          return {
            success: false,
            error: 'Invalid email format. Please check your email and try again.'
          };
        } else {
          return {
            success: false,
            error: error.message || 'Registration failed. Please try again.'
          };
        }
      }

      if (!user) {
        return {
          success: false,
          error: 'Registration failed. Please try again.'
        };
      }

      // Create user profile in the profiles table
      const { error: profileError } = await supabase
        .from('profiles')
        .upsert({
          id: user.id,
          name: userData.name,
          dob: userData.dob || '',
          gender: userData.gender || 'male',
          phone: userData.phone || '',
          address: userData.address || '',
          profile_picture_url: userData.profilePicture || null,
          updated_at: new Date(),
        });

      if (profileError) {
        console.error('Error creating profile:', profileError);
      }

      // Create user object
      const newUser = {
        id: user.id,
        email: userData.email,
        name: userData.name,
        dob: userData.dob || '',
        gender: userData.gender || 'male',
        phone: userData.phone || '',
        address: userData.address || '',
        profilePicture: userData.profilePicture || null,
      };

      // Save the current user to AsyncStorage for persistence
      await AsyncStorage.setItem('lastLoggedInUser', JSON.stringify(newUser));

      setCurrentUser(newUser);

      // Show data permission prompt first, then save prompt will be shown after
      setShowDataPermissionPrompt(true);

      return { success: true };
    } catch (error) {
      console.error('Signup error:', error);
      return {
        success: false,
        error: 'Registration failed. Please try again.'
      };
    }
  };

  // Logout function
  const logout = async () => {
    try {
      // Sign out from Supabase
      const response = await supabase.auth.signOut();
      const error = response?.error || null;

      if (error) {
        console.error('Supabase logout error:', error);
        return {
          success: false,
          error: error.message || 'Logout failed. Please try again.'
        };
      }

      // Remove the last logged in user
      await AsyncStorage.removeItem('lastLoggedInUser');

      // Clear current user state
      setCurrentUser(null);

      return { success: true };
    } catch (error) {
      console.error('Logout error:', error);
      return {
        success: false,
        error: 'Logout failed. Please try again.'
      };
    }
  };

  // Update user profile
  const updateProfile = async (updatedData) => {
    try {
      if (!currentUser?.id) {
        return {
          success: false,
          error: 'User not authenticated'
        };
      }

      // Update profile in Supabase (using v1.x API)
      const { error: profileError } = await supabase
        .from('profiles')
        .upsert({
          id: currentUser.id,
          name: updatedData.name || currentUser.name,
          dob: updatedData.dob || currentUser.dob,
          gender: updatedData.gender || currentUser.gender,
          phone: updatedData.phone || currentUser.phone,
          address: updatedData.address || currentUser.address,
          profile_picture_url: updatedData.profilePicture || currentUser.profilePicture,
          updated_at: new Date(),
        });

      if (profileError) {
        console.error('Error updating profile:', profileError);
        return {
          success: false,
          error: profileError.message || 'Update failed. Please try again.'
        };
      }

      // Update current user state
      const updatedUser = { ...currentUser, ...updatedData };

      // Save the updated user to AsyncStorage for persistence
      await AsyncStorage.setItem('lastLoggedInUser', JSON.stringify(updatedUser));

      setCurrentUser(updatedUser);

      // Update saved account if it exists
      const accounts = await getSavedAccounts();
      const savedAccount = accounts.find(
        account => account.email.toLowerCase() === currentUser.email.toLowerCase()
      );

      if (savedAccount) {
        await saveUserAccount(updatedUser);
      }

      return { success: true };
    } catch (error) {
      console.error('Update profile error:', error);
      return {
        success: false,
        error: 'Update failed. Please try again.'
      };
    }
  };

  // Save the current user account for persistence
  const saveAccount = async () => {
    try {
      if (!currentUser) {
        return {
          success: false,
          error: 'No user to save'
        };
      }

      const result = await saveUserAccount(currentUser);

      if (result) {
        // Refresh saved accounts list
        const accounts = await getSavedAccounts();
        setSavedAccounts(accounts);

        return { success: true };
      }

      return {
        success: false,
        error: 'Failed to save account'
      };
    } catch (error) {
      console.error('Save account error:', error);
      return {
        success: false,
        error: 'Failed to save account'
      };
    }
  };

  // Switch to a different account
  const switchAccount = async (account) => {
    try {
      // We need the password to sign in with Supabase
      if (!account.password) {
        // If we don't have the password, we need to redirect to login
        setCurrentUser(null);
        return {
          success: false,
          needsPassword: true,
          email: account.email
        };
      }

      // Sign in with Supabase (using v1.x API)
      const response = await supabase.auth.signIn({
        email: account.email,
        password: account.password,
      });

      const data = response?.data || null;
      const error = response?.error || null;
      const user = data?.user;

      if (error) {
        console.error('Supabase login error during account switch:', error);

        // If login fails, we need to redirect to login
        setCurrentUser(null);

        // Provide more specific error message
        if (error.message.includes('Invalid login credentials')) {
          return {
            success: false,
            needsPassword: true,
            email: account.email,
            error: 'Incorrect password. Please try again.'
          };
        } else if (error.message.includes('Email not confirmed')) {
          return {
            success: false,
            needsPassword: true,
            email: account.email,
            error: 'Email not verified. Please check your inbox and verify your email.'
          };
        } else {
          return {
            success: false,
            needsPassword: true,
            email: account.email,
            error: error.message || 'Login failed. Please try again.'
          };
        }
      }

      // Get user profile data from the profiles table
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (profileError && profileError.code !== 'PGRST116') {
        console.error('Error fetching profile during account switch:', profileError);
      }

      // Combine auth data with profile data
      const userData = {
        id: user.id,
        email: user.email,
        name: profileData?.name || user.email.split('@')[0],
        dob: profileData?.dob || '',
        gender: profileData?.gender || 'male',
        phone: profileData?.phone || '',
        address: profileData?.address || '',
        profilePicture: profileData?.profile_picture_url || null,
      };

      // Save the current user to AsyncStorage for persistence
      await AsyncStorage.setItem('lastLoggedInUser', JSON.stringify(userData));

      // Set the current user
      setCurrentUser(userData);

      // Update last used timestamp
      await updateLastUsed(account.email);

      // Close account switcher
      setShowAccountSwitcher(false);

      return { success: true };
    } catch (error) {
      console.error('Switch account error:', error);
      return { success: false };
    }
  };

  // Handle data permission acceptance
  const handleDataPermission = async (accepted) => {
    // Close the data permission prompt
    setShowDataPermissionPrompt(false);

    // Store the user's data permission preference
    if (currentUser) {
      // Save the preference to AsyncStorage
      await AsyncStorage.setItem('dataPermissionAccepted', JSON.stringify({
        userId: currentUser.id,
        accepted: accepted,
        timestamp: new Date().toISOString()
      }));

      // If accepted, we save the account information
      // If declined, we don't save any account information
      if (accepted) {
        await saveUserAccount(currentUser);
        // Refresh saved accounts list
        const updatedAccounts = await getSavedAccounts();
        setSavedAccounts(updatedAccounts);
      }

      // Navigate to home after handling data permission
      // This is handled in the SignupScreen component
    }
  };

  // Context value
  const value = {
    currentUser,
    loading,
    savedAccounts,
    showAccountSwitcher,
    setShowAccountSwitcher,
    showDataPermissionPrompt,
    setShowDataPermissionPrompt,
    login,
    signup,
    logout,
    updateProfile,
    saveAccount,
    switchAccount,
    handleDataPermission,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}
