import React, { createContext, useState, useEffect, useContext } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Create the authentication context
const AuthContext = createContext();

// Custom hook to use the auth context
export const useAuth = () => useContext(AuthContext);

// Helper function to get all registered users
const getRegisteredUsers = async () => {
  try {
    const usersJson = await AsyncStorage.getItem('registeredUsers');
    return usersJson ? JSON.parse(usersJson) : [];
  } catch (error) {
    console.error('Error getting registered users:', error);
    return [];
  }
};

// Helper function to save registered users
const saveRegisteredUsers = async (users) => {
  try {
    await AsyncStorage.setItem('registeredUsers', JSON.stringify(users));
  } catch (error) {
    console.error('Error saving registered users:', error);
  }
};

// Create a guest user for skip functionality
const createGuestUser = () => ({
  id: 'guest_' + Date.now(),
  email: '<EMAIL>',
  name: 'Guest User',
  dob: '',
  gender: 'male',
  phone: '',
  address: '',
  profilePicture: null,
  isGuest: true,
});



// Provider component that wraps the app and makes auth object available to any child component that calls useAuth()
export function AuthProvider({ children }) {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Load user data on app start
  useEffect(() => {
    const loadUserData = async () => {
      try {
        // Check if we have a previously logged in user in AsyncStorage
        const lastLoggedInUserJson = await AsyncStorage.getItem('lastLoggedInUser');
        if (lastLoggedInUserJson) {
          const lastLoggedInUser = JSON.parse(lastLoggedInUserJson);
          setCurrentUser(lastLoggedInUser);
        }
      } catch (error) {
        console.error('Error loading user data:', error);
        setCurrentUser(null);
      } finally {
        setLoading(false);
      }
    };

    loadUserData();
  }, []);

  // Login function
  const login = async (email, password) => {
    try {
      // Get registered users from local storage
      const registeredUsers = await getRegisteredUsers();

      // Find user by email
      const user = registeredUsers.find(
        u => u.email.toLowerCase() === email.toLowerCase()
      );

      if (!user) {
        return {
          success: false,
          error: 'Account has not been registered. Please sign up first.'
        };
      }

      // Check password
      if (user.password !== password) {
        return {
          success: false,
          error: 'Incorrect password. Please try again.'
        };
      }

      // Create user data object (excluding password for security)
      const userData = {
        id: user.id,
        email: user.email,
        name: user.name,
        dob: user.dob || '',
        gender: user.gender || 'male',
        phone: user.phone || '',
        address: user.address || '',
        profilePicture: user.profilePicture || null,
      };

      // Save the current user to AsyncStorage for persistence
      await AsyncStorage.setItem('lastLoggedInUser', JSON.stringify(userData));

      setCurrentUser(userData);

      return { success: true };
    } catch (error) {
      console.error('Login error:', error);
      return {
        success: false,
        error: 'Login failed. Please try again.'
      };
    }
  };

  // Signup function
  const signup = async (userData) => {
    try {
      // Get registered users from local storage
      const registeredUsers = await getRegisteredUsers();

      // Check if user already exists
      const existingUser = registeredUsers.find(
        u => u.email.toLowerCase() === userData.email.toLowerCase()
      );

      if (existingUser) {
        return {
          success: false,
          error: 'Account has already been registered. Please login instead.'
        };
      }

      // Create new user with unique ID
      const newUser = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        email: userData.email,
        password: userData.password, // In a real app, this should be hashed
        name: userData.name,
        dob: userData.dob || '',
        gender: userData.gender || 'male',
        phone: userData.phone || '',
        address: userData.address || '',
        profilePicture: userData.profilePicture || null,
        createdAt: new Date().toISOString(),
      };

      // Add to registered users
      registeredUsers.push(newUser);
      await saveRegisteredUsers(registeredUsers);

      // Create user object for current session (excluding password)
      const userSession = {
        id: newUser.id,
        email: newUser.email,
        name: newUser.name,
        dob: newUser.dob,
        gender: newUser.gender,
        phone: newUser.phone,
        address: newUser.address,
        profilePicture: newUser.profilePicture,
      };

      // Save the current user to AsyncStorage for persistence
      await AsyncStorage.setItem('lastLoggedInUser', JSON.stringify(userSession));

      setCurrentUser(userSession);

      return { success: true };
    } catch (error) {
      console.error('Signup error:', error);
      return {
        success: false,
        error: 'Registration failed. Please try again.'
      };
    }
  };

  // Skip to home (guest mode)
  const skipToHome = async () => {
    try {
      const guestUser = createGuestUser();

      // Save guest user to AsyncStorage for persistence
      await AsyncStorage.setItem('lastLoggedInUser', JSON.stringify(guestUser));

      setCurrentUser(guestUser);

      return { success: true };
    } catch (error) {
      console.error('Skip to home error:', error);
      return {
        success: false,
        error: 'Failed to skip to home. Please try again.'
      };
    }
  };

  // Logout function
  const logout = async () => {
    try {
      // Remove the last logged in user
      await AsyncStorage.removeItem('lastLoggedInUser');

      // Clear current user state
      setCurrentUser(null);

      return { success: true };
    } catch (error) {
      console.error('Logout error:', error);
      return {
        success: false,
        error: 'Logout failed. Please try again.'
      };
    }
  };

  // Context value
  const value = {
    currentUser,
    loading,
    login,
    signup,
    logout,
    skipToHome,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}
