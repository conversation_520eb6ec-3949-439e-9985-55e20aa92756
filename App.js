import React, { useState, useEffect } from 'react';
import { View, LogBox } from 'react-native';
import AppNavigator from './Navigation/AppNavigatorNew';
import { AuthProvider } from './contexts/AuthContext';
import { SettingsProvider } from './contexts/SettingsContext';
import SplashScreen from './Screens/SplashScreen';
import GlobalStatusBar from './components/GlobalStatusBar';

// Suppress specific warnings
LogBox.ignoreLogs([
  'VirtualizedLists should never be nested inside plain ScrollViews',
]);

// We'll use dynamic styles based on theme

export default function App() {
  const [showSplash, setShowSplash] = useState(true);

  const handleSplashFinish = () => {
    setShowSplash(false);
  };

  return (
    <AuthProvider>
      <SettingsProvider>
        <View style={{
          flex: 1,
          // Background color will be handled by child components
        }}>
          <GlobalStatusBar />
          {showSplash ? (
            <SplashScreen onFinish={handleSplashFinish} />
          ) : (
            <AppNavigator />
          )}
        </View>
      </SettingsProvider>
    </AuthProvider>
  );
}
