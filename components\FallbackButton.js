import React, { useState, useEffect } from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  Animated,
  View,
} from 'react-native';

const FallbackButton = ({
  onPress,
  text,
  disabled = false,
  style,
  textStyle
}) => {
  // Animation value for the press effect
  const [scaleAnim] = useState(new Animated.Value(1));

  // Handle press in animation
  const handlePressIn = () => {
    try {
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 150,
        useNativeDriver: true,
      }).start();
    } catch (error) {
      console.error('Animation error on press in:', error);
    }
  };

  // Handle press out animation
  const handlePressOut = () => {
    try {
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }).start();
    } catch (error) {
      console.error('Animation error on press out:', error);
    }
  };

  // Reset animation when component unmounts
  useEffect(() => {
    return () => {
      try {
        scaleAnim.setValue(1);
      } catch (error) {
        console.error('Error resetting animation:', error);
      }
    };
  }, []);

  return (
    <Animated.View
      style={[
        styles.buttonContainer,
        { transform: [{ scale: scaleAnim }] },
        style
      ]}
    >
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={() => {
          try {
            if (onPress) onPress();
          } catch (error) {
            console.error('Button press error:', error);
          }
        }}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled}
        style={[
          styles.touchable,
          disabled ? styles.disabledButton : styles.activeButton
        ]}
      >
        <Text style={[styles.buttonText, textStyle]}>
          {text}
        </Text>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  buttonContainer: {
    width: '100%',
    marginBottom: 20,
    borderRadius: 30,
    overflow: 'hidden',
  },
  touchable: {
    width: '100%',
    paddingVertical: 15,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 30,
  },
  activeButton: {
    backgroundColor: '#d442f5',
  },
  disabledButton: {
    backgroundColor: '#666666',
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default FallbackButton;
