import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Pressable } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// Simple colored view that changes when pressed
const ColoredView = ({ children, style, pressed }) => {
  return (
    <View style={[
      style,
      pressed ? styles.pressedButton : styles.normalButton
    ]}>
      {children}
    </View>
  );
};

/**
 * A simplified tab bar component that prioritizes reliability over animations
 */
const SimpleTabBar = ({ state, descriptors, navigation }) => {
  // Safety check for required props
  if (!state || !state.routes || !Array.isArray(state.routes) || !navigation) {
    console.error('Missing required props in SimpleTabBar');
    return null;
  }

  return (
    <View style={styles.container}>
      {state.routes.map((route, index) => {
        // Skip if route is invalid
        if (!route || !route.key) return null;

        // Skip if descriptor is missing
        if (!descriptors || !descriptors[route.key]) return null;

        const { options } = descriptors[route.key];
        const label = options?.title || route.name || '';
        const isFocused = state.index === index;

        // Determine icon based on route
        let iconName;
        if (route.name === 'HomeTab') {
          iconName = isFocused ? 'home' : 'home-outline';
        } else if (route.name === 'ConnectTab') {
          iconName = isFocused ? 'add-circle' : 'add-circle-outline';
        } else if (route.name === 'SettingsTab') {
          iconName = isFocused ? 'settings' : 'settings-outline';
        } else {
          iconName = 'ellipsis-horizontal';
        }

        // Handle tab press
        const onPress = () => {
          try {
            const event = navigation.emit({
              type: 'tabPress',
              target: route.key,
              canPreventDefault: true,
            });

            if (!isFocused && !event.defaultPrevented) {
              navigation.navigate(route.name);
            }
          } catch (error) {
            console.error('Tab navigation error:', error);
          }
        };

        // Special styling for Connect tab
        if (route.name === 'ConnectTab') {
          return (
            <View key={route.key} style={styles.tabItem}>
              <Pressable
                onPress={onPress}
                style={({ pressed }) => [
                  styles.connectButton,
                  pressed && styles.buttonPressed
                ]}
              >
                {({ pressed }) => (
                  <View style={styles.connectButtonWrapper}>
                    <ColoredView
                      style={styles.connectButtonInner}
                      pressed={pressed}
                    >
                      <Ionicons name={iconName} size={28} color="#FFFFFF" />
                    </ColoredView>
                    <Text style={styles.connectLabel}>{label}</Text>
                  </View>
                )}
              </Pressable>
            </View>
          );
        }

        // Regular tab
        return (
          <View key={route.key} style={styles.tabItem}>
            <Pressable
              onPress={onPress}
              style={({ pressed }) => [
                styles.tab,
                pressed && styles.tabPressed
              ]}
            >
              {({ pressed }) => (
                <>
                  <Ionicons
                    name={iconName}
                    size={24}
                    color={isFocused ? '#FFFFFF' : (pressed ? '#d0d0d0' : '#9e9e9e')}
                  />
                  <Text style={[
                    styles.label,
                    { color: isFocused ? '#FFFFFF' : (pressed ? '#d0d0d0' : '#9e9e9e') }
                  ]}>
                    {label}
                  </Text>
                </>
              )}
            </Pressable>
          </View>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  normalButton: {
    backgroundColor: '#d442f5', // Consistent pink-purple color
  },
  pressedButton: {
    backgroundColor: '#9c2eb6', // Darker when pressed
  },
  buttonPressed: {
    transform: [{ scale: 0.95 }],
  },
  tabPressed: {
    opacity: 0.8,
  },
  container: {
    flexDirection: 'row',
    backgroundColor: '#1e1e1e',
    height: 70, // Increased height to accommodate the text below the connect button
    borderTopColor: '#333333',
    borderTopWidth: 1,
    paddingBottom: 5, // Add some padding at the bottom
  },
  tabItem: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tab: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 5,
    width: '100%',
  },
  label: {
    fontSize: 12,
    marginTop: 2,
  },
  connectButton: {
    width: '90%',
    overflow: 'hidden',
    marginTop: -30, // Raised even higher to accommodate text below
    marginBottom: 5, // Add space at the bottom
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
  },
  connectButtonWrapper: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  connectButtonInner: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 50, // Make it more circular
    height: 50, // Make it more circular
    borderRadius: 25, // Make it perfectly round
    borderWidth: 2,
    borderColor: '#1e1e1e',
    // backgroundColor is now set by normalButton/pressedButton
  },
  connectLabel: {
    color: '#FFFFFF',
    fontSize: 11,
    fontWeight: 'bold',
    marginTop: 4, // Space between icon and text
  },
});

export default SimpleTabBar;
