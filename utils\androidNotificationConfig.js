import { Platform } from 'react-native';

/**
 * Configure Android notification channels (stub)
 * This function is a stub that doesn't actually configure channels
 */
export const configureAndroidNotificationChannels = async () => {
  if (Platform.OS !== 'android') {
    return;
  }

  console.log('[NOTIFICATION STUB] Would configure Android notification channels');
};

/**
 * Get the appropriate channel ID based on notification type (stub)
 * @param {string} type Notification type
 * @returns {string} Channel ID
 */
export const getChannelId = (type) => {
  return 'default';
};

export default {
  configureAndroidNotificationChannels,
  getChannelId,
};
